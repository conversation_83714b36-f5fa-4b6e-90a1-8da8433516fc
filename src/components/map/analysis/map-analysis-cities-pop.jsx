import { useEffect, useState } from "react";
import { Layer, Source } from "react-map-gl/mapbox";
import { functions } from "../../../service/functions";

export default function MapAnalysisCitiesPop({ mapRef, center, radius = 200 }) {
  const [cities, setCities] = useState(null);

  useEffect(() => {
    if (center) {
      functions.data.geo.gacwp(center[0], center[1], radius).then((data) => {
        setCities(
          data.features.results.map((feature) => ({
            coordinates: [feature.coordinates.lon, feature.coordinates.lat],
            population: feature.population,
            name: feature.name,
          }))
        );
      });
    }
  }, [center, radius]);

  if (!mapRef.current) return null;
  return (
    <>
      {cities && (
        <Source
          type="geojson"
          data={{
            type: "FeatureCollection",
            features: cities.map((city) => ({
              type: "Feature",
              properties: {
                population: city.population,
                name: city.name,
              },
              geometry: {
                type: "Point",
                coordinates: city.coordinates,
              },
            })),
          }}
        >
          <Layer
            id="population-heatmap"
            type="heatmap"
            paint={{
              // Rayon adaptatif selon le zoom
              "heatmap-radius": [
                "interpolate",
                ["linear"],
                ["zoom"],
                0,
                5,
                9,
                30,
                15,
                50,
              ],
              // Poids basé sur la population de chaque ville
              "heatmap-weight": [
                "interpolate",
                ["linear"],
                ["get", "population"],
                0,
                0, // Pas de population = pas de poids
                1000,
                0.1, // Petites villes
                5000,
                0.3, // Villes moyennes
                20000,
                0.6, // Grandes villes
                50000,
                0.8, // Très grandes villes
                100000,
                1.0, // Métropoles
              ],
              // Intensité selon le niveau de zoom
              "heatmap-intensity": [
                "interpolate",
                ["linear"],
                ["zoom"],
                0,
                0.5,
                9,
                1.5,
                15,
                2,
              ],
              // Gradient de couleurs plus sophistiqué
              "heatmap-color": [
                "interpolate",
                ["linear"],
                ["heatmap-density"],
                0,
                "rgba(33,102,172,0)", // Transparent
                0.1,
                "rgba(103,169,207,0.3)", // Bleu très clair
                0.3,
                "rgba(209,229,240,0.6)", // Bleu clair
                0.5,
                "rgba(253,219,199,0.8)", // Orange clair
                0.7,
                "rgba(239,138,98,0.9)", // Orange
                0.9,
                "rgba(215,48,39,1)", // Rouge
                1,
                "rgba(178,24,43,1)", // Rouge foncé
              ],
              // Opacité qui diminue quand on zoome trop
              "heatmap-opacity": [
                "interpolate",
                ["linear"],
                ["zoom"],
                5,
                0.8,
                10,
                0.6,
                15,
                0.3,
              ],
            }}
          />
        </Source>
      )}
    </>
  );
}
