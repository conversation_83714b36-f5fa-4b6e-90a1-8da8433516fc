import { useEffect, useState } from "react";
import { Layer, Source } from "react-map-gl/mapbox";
import { functions } from "../../../service/functions";

export default function MapAnalysisCitiesPop({ mapRef, center, radius = 200 }) {
  const [cities, setCities] = useState(null);

  useEffect(() => {
    if (center) {
      functions.data.geo.gacwp(center[0], center[1], radius).then((data) => {
        setCities(
          data.features.results.map((feature) => ({
            coordinates: [feature.coordinates.lon, feature.coordinates.lat],
            population: feature.population,
            name: feature.name,
          }))
        );
      });
    }
  }, [center, radius]);

  if (!mapRef.current) return null;
  return (
    <>
      {cities && (
        <Source
          type="geojson"
          data={{
            type: "FeatureCollection",
            features: cities.map((city) => ({
              type: "Feature",
              properties: {
                population: city.population,
                name: city.name,
              },
              geometry: {
                type: "Point",
                coordinates: city.coordinates,
              },
            })),
          }}
        >
          <Layer
            type="circle"
            paint={{
              "circle-radius": [
                "interpolate",
                ["linear"],
                ["get", "population"],
                0,
                2,
                1000,
                5,
                5000,
                8,
                20000,
                12,
                50000,
                16,
                100000,
                20,
              ],
              "circle-color": [
                "interpolate",
                ["linear"],
                ["get", "population"],
                0,
                "#ffff00", // Jaune pour petites populations
                1000,
                "#ff8800", // Orange
                5000,
                "#ff4400", // Rouge-orange
                20000,
                "#ff0000", // Rouge
                50000,
                "#cc0000", // Rouge foncé
                100000,
                "#880000", // Rouge très foncé
              ],
              "circle-opacity": 0.8,
              "circle-stroke-width": 1,
              "circle-stroke-color": "#ffffff",
            }}
          />
        </Source>
      )}
    </>
  );
}
