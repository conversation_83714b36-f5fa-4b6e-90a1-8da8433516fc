import { useEffect, useState } from "react";
import { Layer, Source } from "react-map-gl/mapbox";
import { functions } from "../../../service/functions";

export default function MapAnalysisCitiesPop({ mapRef, center, radius = 200 }) {
  const [cities, setCities] = useState(null);

  useEffect(() => {
    if (center) {
      functions.data.geo.gacwp(center[0], center[1], radius).then((data) => {
        setCities(
          data.features.results.map((feature) => ({
            coordinates: [feature.coordinates.lon, feature.coordinates.lat],
            population: feature.population,
            name: feature.name,
          }))
        );
      });
    }
  }, [center, radius]);

  if (!mapRef.current) return null;
  return (
    <>
      {cities && (
        <Source
          type="geojson"
          data={{
            type: "FeatureCollection",
            features: cities.map((city) => {
              // Créer un petit carré autour de chaque ville pour l'extrusion
              const [lon, lat] = city.coordinates;
              const size = 0.01; // <PERSON>lle du carré en degrés

              return {
                type: "Feature",
                properties: {
                  population: city.population,
                  name: city.name,
                },
                geometry: {
                  type: "Polygon",
                  coordinates: [
                    [
                      [lon - size, lat - size],
                      [lon + size, lat - size],
                      [lon + size, lat + size],
                      [lon - size, lat + size],
                      [lon - size, lat - size], // Fermer le polygone
                    ],
                  ],
                },
              };
            }),
          }}
        >
          <Layer
            type="fill-extrusion"
            paint={{
              "fill-extrusion-height": [
                "interpolate",
                ["linear"],
                ["get", "population"],
                0,
                0,
                1000,
                500,
                5000,
                1500,
                20000,
                3000,
                50000,
                6000,
                100000,
                10000,
                500000,
                20000,
              ],
              "fill-extrusion-color": [
                "interpolate",
                ["linear"],
                ["get", "population"],
                0,
                "#ffff00", // Jaune pour petites populations
                1000,
                "#ff8800", // Orange
                5000,
                "#ff4400", // Rouge-orange
                20000,
                "#ff0000", // Rouge
                50000,
                "#cc0000", // Rouge foncé
                100000,
                "#880000", // Rouge très foncé
              ],
              "fill-extrusion-opacity": 0.8,
              "fill-extrusion-base": 0,
            }}
          />
        </Source>
      )}
    </>
  );
}
